locals {
  fe-ppe-deployments-ui = {
    "version" : "v0.4.0"
  }

  fe-ppe-status-bff = {
    "version" : "v0.2.2"
  }

  NAMESPACE = "fe-ppe"

  IMAGE_REPO  = "633377509572.dkr.ecr.us-east-1.amazonaws.com"

  UI_SERVER_PORT = 80
  BFF_SERVER_PORT = 3000
  PODMONITOR_PORT = 9090

  KC_HOST_URL = "http://ia-keycloak-spi.kc-op.svc.cluster.local:8080"
  KC_REALM    = "invenco-hub"

  HTTP_CLIENT_TIMEOUT = "30s"
  HTTP_CLIENT_RETRY_MAX = "3"

  LOG_LEVEL = "info"
  SERVER_PORT = "3000"
  SERVER_READ_TIMEOUT = "500s"
  LOGS_API_URL = "http://dp-ppe-logs-api.dp.svc.cluster.local:8080"
  LOOKUP_API_URL = "https:/ia-lookup-api.ia.svc.cluster.local:8080"

  RSU_API_URL = "http://rsu-api.ia.svc.cluster.local:8080"
  PPE_DEPLOYMENTS_API_URL = "http://cp-ppe-logs-api.cp.svc.cluster.local:8080"
  IA_SITE_API_URL = "http://ia-site-api.ia.svc.cluster.local:8080"
  UCC_PROPS_API_URL = "http://dp-ucc-props-api.dp.svc.cluster.local:8080"
}
