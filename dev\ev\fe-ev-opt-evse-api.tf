# fe-ev-opt-evse-api deployment
resource "kubernetes_manifest" "fe_ev_opt_evse_api_deployment" {
  manifest = yamldecode(templatefile("../../common/ev/fe-ev-opt-evse-api/base-deployment.yml", {
    name      = "fe-ev-opt-evse-api"
    NAMESPACE = local.namespace
    REPLICAS  = 1

    image_repo = local.image_repo
    image_tag  = local.fe-ev-opt-evse-api.version

    CPU_REQUEST = "10m"
    MEM_REQUEST = "100Mi"

    LOG_LEVEL           = "info"
    SERVER_PORT         = local.server_port
    SERVER_READ_TIMEOUT = local.server_timeout


    MONGO_SCHEMA        = local.mongo_schema
    MONGO_HOST          = local.mongo_hostname_common
    MONGO_AUTH_SOURCE   = local.mongo_auth_source
    MONGO_OPTIONS       = local.mongo_options_common
    MONGO_MAX_POOL_SIZE = "100"
    MONGO_TIMEOUT       = "30s"
    MONGO_QUERY_LIMIT   = "25"


    MONGO_DATABASE        = local.mongo_database_common
    MONGO_CREDENTIALS     = local.mongo_credentials_opt_evse_api
    MONGO_COLLECTION      = local.mongo_opt_evse_collection
  }))

  computed_fields = ["spec.template.metadata.annotations"]
  
  field_manager {
    force_conflicts = true
  }
}

# fe-ev-opt-evse-api service
resource "kubernetes_service" "fe_ev_opt_evse_api_service" {
  metadata {
    name      = "fe-ev-opt-evse-api"
    namespace = local.namespace
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "fe-ev-opt-evse-api"
    }
    port {
      port        = local.server_port
      target_port = local.server_port
    }
    type = "NodePort"
  }
}

#Vault Dynamic Secret for MongoDB
resource "kubernetes_manifest" "fe_ev_opt_evse_api_vds" {
  manifest = yamldecode(templatefile("../../common/ev/fe-ev-opt-evse-api/vds.yml", {
    MONGO_ROLE = "fe-ev-opt-evse-api"
    name       = "fe-ev-opt-evse-api"
    env        = "dev"
  }))

  field_manager {
    force_conflicts = true
  }
}

# Tell prometheus to scrape these metrics
resource "kubernetes_manifest" "fe_ev_opt_evse_api_podmonitor" {
  manifest = yamldecode(templatefile("../../common/prometheus/podmonitor.yml.tftpl", {
    NAME        = "fe-ev-opt-evse-api"
    NAMESPACE   = "fe-ev"
    TARGET_PORT = local.server_port
  }))
}
