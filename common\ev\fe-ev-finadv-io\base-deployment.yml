---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: ${name}
  name: ${name}
  namespace: ${NAMESPACE}
spec:
  replicas: "${REPLICAS}"
  selector:
    matchLabels:
      app: ${name}
  template:
    metadata:
      annotations:
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
      labels:
        app: ${name}
    spec:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
        - name: ${name}
          image: ${image_repo}/${name}:${image_tag}
          ports:
            - containerPort: ${METRICS_SERVER_PORT}
          resources:
            requests:
              cpu: ${CPU_REQUEST}
              memory: ${MEM_REQUEST}
          env:
            - name: LOG_LEVEL
              value: "${LOG_LEVEL}"
            - name: SERVER_READ_TIMEOUT
              value: "${SERVER_READ_TIMEOUT}"
            - name: METRICS_SERVER_PORT
              value: "${METRICS_SERVER_PORT}"

            - name: KC_HOST
              value: "${KC_HOST}"
            - name: KC_REALM
              value: "${KC_REALM}"
            - name: AUTH_CLIENT_ID
              value: "${AUTH_CLIENT_ID}"
            - name: AUTH_CLIENT_SECRET
              valueFrom:
                secretKeyRef:
                  name: fe-ev-secrets
                  key: common-keycloack-ivenco-hub-secret
            - name: AUTH_SERVICE_USERNAME
              value: "${AUTH_SERVICE_USERNAME}"
            - name: AUTH_SERVICE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: fe-ev-secrets
                  key: fe-ev-finadv-io-hubacc-password

            - name: MONGO_AUTH_SOURCE
              value: "${MONGO_AUTH_SOURCE}"
            - name: MONGO_DATABASE
              value: "${MONGO_DATABASE}"
            - name: MONGO_HOST
              value: "${MONGO_HOST}"
            - name: MONGO_PORT
              value: "${MONGO_PORT}"
            - name: MONGO_OPTIONS
              value: "${MONGO_OPTIONS}"
            - name: MONGO_USERNAME
              valueFrom:
                secretKeyRef:
                  name: "${MONGO_CREDENTIALS}"
                  key: username
            - name: MONGO_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: "${MONGO_CREDENTIALS}"
                  key: password
            - name: MONGO_SCHEMA
              value: "${MONGO_SCHEMA}"
            - name: MONGO_COLLECTION
              value: "${MONGO_COLLECTION}"

            - name: RTD_ENABLED
              value: "${RTD_ENABLED}"
            - name: RTD_AUTH_ENABLED
              value: "${RTD_AUTH_ENABLED}"
            - name: RTD_URL
              value: "${RTD_URL}"
            - name: RTD_STREAM
              value: "${RTD_STREAM}"
            - name: RTD_CLIENT_NAME
              value: "${RTD_CLIENT_NAME}"
            - name: RTD_SUBJECT_FILTER
              value: "${RTD_SUBJECT_FILTER}"
            - name: TEMPLATE_URL
              value: "${TEMPLATE_URL}"
            - name: OPT_EVSE_URL
              value: "${OPT_EVSE_URL}"
            - name: UCC_URL
              value: "${UCC_URL}"
              
              

