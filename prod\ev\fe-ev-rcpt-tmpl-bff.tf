# fe-ev-rcpt-tmpl-bff deployment
resource "kubernetes_manifest" "fe_ev_rcpt_tmpl_bff_deployment" {
  manifest = yamldecode(templatefile("../../common/ev/fe-ev-rcpt-tmpl-bff/base-deployment.yml", {
    name      = "fe-ev-rcpt-tmpl-bff"
    NAMESPACE = local.namespace
    REPLICAS  = 0

    image_repo = local.image_repo
    image_tag  = local.fe-ev-rcpt-tmpl-bff.version

    LOG_LEVEL           = "info"
    SERVER_PORT         = local.server_port
    SERVER_READ_TIMEOUT = local.server_timeout

    HTTP_CLIENT_TIMEOUT      = "30s"
    HTTP_CLIENT_RETRY_MAX    = "3"

    TEMPLATE_API_URL = local.template_url

    KC_HOST               = "http://ia-keycloak-spi.kc-op.svc.cluster.local:8080"
    KC_REALM              = "invenco-hub"
    AUTH_CLIENT_ID        = "invenco-client"
    AUTH_SERVICE_USERNAME = "<EMAIL>"

    MONGO_SCHEMA        = local.mongo_schema
    MONGO_HOST          = local.mongo_hostname_common
    MONGO_AUTH_SOURCE   = local.mongo_auth_source
    MONGO_OPTIONS       = local.mongo_options_common
    MONGO_MAX_POOL_SIZE = "100"
    MONGO_TIMEOUT       = "30s"
    MONGO_QUERY_LIMIT   = "25"


    MONGO_DATABASE        = local.mongo_database_secure 
    MONGO_CREDENTIALS     = local.mongo_credentials_rcpt_bff
    MONGO_COLLECTION      = local.mongo_connection_collection
  }))

  computed_fields = ["spec.template.metadata.annotations"]
  
  field_manager {
    force_conflicts = true
  }
}

# fe-ev-rcpt-tmpl-bff service
resource "kubernetes_service" "fe_ev_rcpt_tmpl_bff_service" {
  metadata {
    name      = "fe-ev-rcpt-tmpl-bff"
    namespace = local.namespace
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "fe-ev-rcpt-tmpl-bff"
    }
    port {
      port        = local.server_port
      target_port = local.server_port
    }
    type = "NodePort"
  }
}

#Vault Dynamic Secret for MongoDB
resource "kubernetes_manifest" "fe_ev_rcpt_tmpl_bff_vds" {
  manifest = yamldecode(templatefile("../../common/ev/fe-ev-rcpt-tmpl-bff/vds.yml", {
    MONGO_ROLE = "fe-ev-rcpt-tmpl-bff"
    name       = "fe-ev-rcpt-tmpl-bff"
    env        = "prod"
  }))

  field_manager {
    force_conflicts = true
  }
}

# Tell prometheus to scrape these metrics
resource "kubernetes_manifest" "fe_ev_rcpt_tmpl_bff_podmonitor" {
  manifest = yamldecode(templatefile("../../common/prometheus/podmonitor.yml.tftpl", {
    NAME        = "fe-ev-rcpt-tmpl-bff"
    NAMESPACE   = "fe-ev"
    TARGET_PORT = local.server_port
  }))
}
