---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: fe-wsm-bff
  name: fe-wsm-bff
  namespace: fe-wsm
spec:
  replicas: "${REPLICAS}"
  selector:
    matchLabels:
      app: fe-wsm-bff
  template:
    metadata:
      labels:
        app: fe-wsm-bff
    spec:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
        - name: fe-wsm-bff
          image: ${image_repo}/fe-wsm-bff:${image_tag}
          ports:
            - containerPort: "${SERVER_PORT}"
          resources:
            requests:
              cpu: ${CPU_REQUEST}
              memory: ${MEM_REQUEST}
          env:
            - name: LOG_LEVEL
              value: "${LOG_LEVEL}"
            - name: SERVER_PORT
              value: "${SERVER_PORT}"
            - name: IA_APP_REG_URI
              value: "${IA_APP_REG_URI}"
            - name: SERVER_READ_TIMEOUT
              value: "${SERVER_READ_TIMEOUT}"

            - name: KEY<PERSON>OAK_HOST
              value: "${KEYCLOAK_HOST}"
            - name: KEYCLOAK_REALM
              value: "${KEYCLOAK_REALM}"

            - name: HTTP_CLIENT_TIMEOUT
              value: "${HTTP_CLIENT_TIMEOUT}"
            - name: HTTP_CLIENT_RETRY_MAX
              value: "${HTTP_CLIENT_RETRY_MAX}"

            - name: TOPO_API_URL
              value: "${TOPO_API_URL}"
            - name: INVENTORY_API_URL
              value: "${INVENTORY_API_URL}"
            - name: SALES_API_URL
              value: "${SALES_API_URL}"
            - name: MHR_RECON_API_URL
              value: "${MHR_RECON_API_URL}"
            - name: DAILY_SALE_API_URL
              value: "${DAILY_SALE_API_URL}"
            - name: FPM_STATUS_API_URL
              value: "${FPM_STATUS_API_URL}"
            - name: COMPANY_API_URL
              value: "${COMPANY_API_URL}"
            - name: SITE_API_URL
              value: "${SITE_API_URL}"
            - name: TANK_STATUS_API_URL
              value: "${TANK_STATUS_API_URL}"
            - name: METER_VAR_API_URL
              value: "${METER_VAR_API_URL}"

            - name: COMPANY_MOCK_TYPE
              value: "${COMPANY_MOCK_TYPE}"
            - name: SITE_MOCK_TYPE
              value: "${SITE_MOCK_TYPE}"
            - name: FPM_STATUS_MOCK_TYPE
              value: "${FPM_STATUS_MOCK_TYPE}"
            - name: CONTRACT_API_URL
              value: "${CONTRACT_API_URL}"
            - name: DISPENSER_STATUS_API_URL
              value: "${DISPENSER_STATUS_API_URL}"

            - name: METER_FLOW_API_URL
              value: "${METER_FLOW_API_URL}"