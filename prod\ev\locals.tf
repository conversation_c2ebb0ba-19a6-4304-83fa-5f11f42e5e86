locals {
  fe-ev-version = "v0.27.0"
  fe-ev-csms-api = {
    "version" : "v1.2.9",
    "host" : "elvis.hub.invenco.com"
  }
  fe-ev-csms-io = {
    "version" : "v1.3.2",
  }
  fe-ev-location-in = {
    "version" : "v1.1.3",
  }
  fe-ev-location-api = {
    "version" : "v1.0.8",
  }
  fe-ev-obj-update-io = {
    "version" : "v1.2.7",
  }
  fe-ev-session-in = {
    "version" : "v1.1.8",
  }
  fe-ev-session-api = {
    "version" : "v1.0.5",
  }
  fe-ev-cdr-in = {
    "version" : "v1.1.4",
  }
  fe-ev-cdr-api = {
    "version" : "v1.0.1",
  }
  fe-ev-tariff-in = {
    "version" : "v1.1.1",
  }
  fe-ev-tariff-api = {
    "version" : "v1.0.1",
  }
  fe-ev-finadv-io = {
    "version" : "v1.0.8",
  }
  fe-ev-e2e = {
    "version" : "v0.0.1",
  }
  fe-ev-rcpt-api = {
    "version" : "v1.0.3",
    "host" : "konect-receipts.hub.invenco.com"
  }
  fe-ev-rcpt-tmpl-api = {
    "version" : "v1.6.0",
  }
  fe-ev-opt-evse-api = {
    "version" : "v1.1.3",
  }
  fe-ev-rcpt-tmpl-ui = {
    "version" : "v1.2.2",
  }
  fe-ev-opt-ui = {
    "version" : "v1.5.2"
  }
  fe-ev-rcpt-tmpl-bff = {
    "version" : "v1.1.6",
  }

  fe-ev-smsrcpt-io = {
    "version" : "v0.0.2",
  }

  fe-ev-opt-bff = {
    "version" : "v0.0.39",
  }

  image_repo     = "633377509572.dkr.ecr.us-east-1.amazonaws.com"
  server_port    = 8080
  metrics_server_port = "9090"
  server_timeout = "60s"

  mongo_auth_source             = "admin"
  mongo_port                    = 27017
  mongo_schema                  = "mongodb+srv"
  mongo_hostname_common         = "prod-fe-ev-common-pl-0.hyzy0.mongodb.net"
  mongo_hostname_secure         = "prod-fe-ev-secure-pl-0.hyzy0.mongodb.net"
  mongo_database_secure         = "fe-ev-secure"
  mongo_database_common         = "fe-ev-common"
  mongo_options_secure          = "retryWrites=true&w=majority&appName=prod-fe-ev-secure"
  mongo_options_common          = "retryWrites=true&w=majority&appName=prod-fe-ev-common"
  mongo_connection_collection   = "connections"
  mongo_location_collection     = "locations"
  mongo_session_collection      = "sessions"
  mongo_cdr_collection          = "cdrs"
  mongo_rcpt_tmpl_collection    = "templates"  
  mongo_receipt_collection      = "receipts"
  mongo_tariff_collection       = "tariffs"
  mongo_opt_evse_collection     = "evses" 

  mongo_credentials_location_in = "fe-ev-location-in-vds"
  mongo_credentials_csms_api    = "fe-ev-csms-api-vds"
  mongo_credentials_csms_io     = "fe-ev-csms-io-vds"
  mongo_credentials_session_in  = "fe-ev-session-in-vds"
  mongo_credentials_cdr_in      = "fe-ev-cdr-in-vds"
  mongo_credentials_tariff_in   = "fe-ev-tariff-in-vds"
  mongo_credentials_finadv_io   = "fe-ev-finadv-io-vds"
  mongo_credentials_opt_evse_api= "fe-ev-opt-evse-api-vds"
  mongo_credentials_opt_bff     = "fe-ev-opt-bff-vds"
  mongo_credentials_rcpt_bff    = "fe-ev-rcpt-tmpl-bff-vds"


  keycloak_host          = "http://ia-keycloak-spi.kc-op.svc.cluster.local:8080"
  keycloak_invenco_realm = "invenco-hub"
  invenco_client_id      = "invenco-client"

  device_class_filter    = "opt"
  device_type_filter     = "konect"

  location_url  = "http://fe-ev-location-api.fe-ev.svc.cluster.local:8080"
  session_url   = "http://fe-ev-session-api.fe-ev.svc.cluster.local:8080"
  cdr_url       = "http://fe-ev-cdr-api.fe-ev.svc.cluster.local:8080"
  tariff_url    = "http://fe-ev-tariff-api.fe-ev.svc.cluster.local:8080"
  template_url  = "http://fe-ev-rcpt-tmpl-api.fe-ev.svc.cluster.local:8080"
  opt_evse_url  = "http://fe-ev-opt-evse-api.fe-ev.svc.cluster.local:8080"
  dev_reg_url   = "http://ia-devreg-api.ia.svc.cluster.local:8080"
  ucc_props_url = "http://dp-ucc-props-api.dp.svc.cluster.local:8080"
  ucc_cmd_url   = "http://cp-ucc-cmd-api.cp.svc.cluster.local:8080"


  namespace = "fe-ev"

  ocpi_versions = [
    "2.2",
    "2.2.1"
  ]
  ocpi_modules = [
    "credentials",
    "locations",
    "sessions",
    "tokens",
    "cdrs",
    "commands",
    "tariffs"
  ]

  rtd_auth_enabled   = true
  rtd_url            = "nats://nats.fe.svc.cluster.local:4222"
  rtd_stream         = "fe-ev-csms-api"
  rtd_client_name    = "fe-ev-location-in-client"
  rtd_location_subject_filter   = "fe-ev-csms-api.locationUpdate.>"
  rtd_session_subject_filter    = "fe-ev-csms-api.sessionUpdate.>"
  rtd_cdr_subject_filter        = "fe-ev-csms-api.cdrUpdate.>"
  rtd_tariff_subject_filter     = "fe-ev-csms-api.tariffUpdate.>"
  rtd_subject_filters = [
    "fe-ev-csms-api.locationUpdate.>",
    "fe-ev-csms-api.sessionUpdate.>",
    "fe-ev-csms-api.cdrUpdate.>",
    "fe-ev-csms-api.commandUpdate.>",
    "fe-ev-csms-api.tariffUpdate.>"
  ]
  csms_io_filters = [
    "dp-ucc-telem-in.eds-ev.>"
  ]
  allowed_locations = ["8", "10", "11"]
}
