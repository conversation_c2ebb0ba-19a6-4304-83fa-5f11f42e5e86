

resource "kubernetes_manifest" "fe_ppe_status_bff_deployment" {
  manifest = yamldecode(templatefile("../../common/ppe/fe-ppe-status-bff/base-deployment.yml", {
    image_repo = local.IMAGE_REPO
    image_tag  = local.fe-ppe-status-bff.version

    NAMESPACE = local.NAMESPACE
    NAME      = "fe-ppe-status-bff"
    REPLICAS  = 1

    LOG_LEVEL           = "info"
    SERVER_PORT         = local.BFF_SERVER_PORT
    SERVER_READ_TIMEOUT = local.SERVER_READ_TIMEOUT
    HTTP_CLIENT_TIMEOUT = local.HTTP_CLIENT_TIMEOUT
    HTTP_CLIENT_RETRY_MAX = local.HTTP_CLIENT_RETRY_MAX

    KC_HOST  = local.KC_HOST_URL
    KC_REALM = local.KC_REALM

    LOGS_API_URL    = local.LOGS_API_URL
    LOOKUP_API_URL   = local.LOOKUP_API_URL
    RSU_API_URL = local.RSU_API_URL
    PPE_DEPLOYMENTS_API_URL       = local.PPE_DEPLOYMENTS_API_URL
    IA_SITE_API_URL    = local.IA_SITE_API_URL
    UCC_PROPS_API_URL   = local.UCC_PROPS_API_URL
  }))

  field_manager {
    force_conflicts = true
  }
}

resource "kubernetes_service" "fe_ppe_status_bff_service" {
  metadata {
    name      = "fe-ppe-status-bff"
    namespace = local.NAMESPACE
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "fe-ppe-status-bff"
    }
    port {
      port        = local.BFF_SERVER_PORT
      target_port = local.BFF_SERVER_PORT
    }

    type = "ClusterIP"
  }
}

# Tell prometheus to scrape these metrics
resource "kubernetes_manifest" "fe_ppe_status_bff_podmonitor" {
  manifest = yamldecode(templatefile("../../common/prometheus/podmonitor.yml.tftpl", {
    NAME        = "fe-ppe-status-bff"
    NAMESPACE   = local.NAMESPACE
    TARGET_PORT = local.PODMONITOR_PORT
  }))
}