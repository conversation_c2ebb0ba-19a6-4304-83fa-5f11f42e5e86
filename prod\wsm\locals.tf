locals {
  fe-wsm-version = "v0.3.0"

  fe-wsm-bff = {
    "version" : "v0.4.5"
  }

  fe-wsm-ui = {
    "version" : "v0.3.16"
  }

  keycloak_host = "http://ia-keycloak-spi.kc-op.svc.cluster.local:8080"
  keycloak_realm = "invenco-hub"

  topo_api_url = "http://dp-topo-api-v1.dp.svc.cluster.local:8080"
  appreg_api_url = "http://ia-appreg-api.ia.svc.cluster.local:8080"

  image_repo = "633377509572.dkr.ecr.us-east-1.amazonaws.com"

  minutes_session_token_expiry = "30"
}
