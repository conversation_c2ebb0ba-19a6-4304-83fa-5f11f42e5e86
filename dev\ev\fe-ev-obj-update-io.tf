resource "kubernetes_manifest" "fe_ev_obj_update_io_deployment" {
  manifest = yamldecode(templatefile("../../common/ev/fe-ev-obj-update-io/base-deployment.yml", {
    name      = "fe-ev-obj-update-io"
    NAMESPACE = local.namespace
    REPLICAS  = 1

    image_repo = local.image_repo
    image_tag  = local.fe-ev-obj-update-io.version

    CPU_REQUEST = "10m"
    MEM_REQUEST = "100Mi"

    LOG_LEVEL           = "info"
    SERVER_PORT         = "9090" // uses for metrics
    SERVER_READ_TIMEOUT = local.server_timeout
    WORKER_COUNT        = "10"

    KC_HOST               = "http://ia-keycloak-spi.kc-op.svc.cluster.local:8080"
    KC_REALM              = "invenco-hub"
    AUTH_CLIENT_ID        = "invenco-client"
    AUTH_SERVICE_USERNAME = "<EMAIL>"

    HTTP_CLIENT_TIMEOUT      = "30s"
    HTTP_CLIENT_RETRY_MAX    = "3"
    HTTP_CLIENT_TIMEOUT      = "1s"
    HTTP_UCC_ENDPOINT        = "/operations"
    HTTP_UCC_HOST            = "http://cp-ucc-cmd-api.cp.svc.cluster.local:8080"
    HTTP_OPT_EVSE_HOST       = local.opt_evse_url
    HTTP_OPT_EVSEENDPOINT    = "/device/evse"
    HTTP_UCC_COMMAND         = "eds-ev.objectUpdate"
    RTD_URL            = local.rtd_url
    RTD_STREAM         = local.rtd_stream
    RTD_CLIENT_NAME    = "fe-ev-obj-update-io"
    RTD_SUBJECT_FILTERS = join(",", local.rtd_subject_filters)
    LOCATION_ENDPOINT   = local.location_url
    RTD_AUTH_ENABLED   = local.rtd_auth_enabled
  }))
  field_manager {
    force_conflicts = true
  }
}


resource "kubernetes_service" "fe_ev_obj_update_io_service" {
  metadata {
    name      = "fe-ev-obj-update-io"
    namespace = local.namespace
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "fe-ev-obj-update-io"
    }
    port {
      port        = local.server_port
      target_port = local.server_port
    }
    type = "NodePort"
  }
}

# Tell prometheus to scrape these metrics
resource "kubernetes_manifest" "fe_ev_obj_update_io_podmonitor" {
  manifest = yamldecode(templatefile("../../common/prometheus/podmonitor.yml.tftpl", {
    NAME        = "fe-ev-obj-update-io"
    NAMESPACE   = "fe-ev"
    TARGET_PORT = 9090
  }))
}