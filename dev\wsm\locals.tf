locals {
  fe-wsm-version = "v0.3.0"

  fe-wsm-bff = {
    "version" : "v0.4.5"
  }

  fe-wsm-ui = {
    "version" : "v0.3.16"
  }

  fe-wsm-e2e = {
    "version" : "v1.0.0"
  }

  fe-wsm-playwright-e2e = {
    "version" : "v1.0.18"
  }

  fe-wsm-mongo-setup = {
    version = "v1.0.0"
  }

  keycloak_host = "http://ia-keycloak-spi.kc-op.svc.cluster.local:8080"
  keycloak_realm = "invenco-hub"

  topo_api_url = "http://dp-topo-api-v1.dp.svc.cluster.local:8080"
  appreg_api_url = "http://ia-appreg-api.ia.svc.cluster.local:8080"

  image_repo = "633377509572.dkr.ecr.us-east-1.amazonaws.com"

  mongo_hostname = "ava-pl-0.iq83ct.mongodb.net"
  mongo_port = 27017
  mongo_schema = "mongodb+srv"
}
