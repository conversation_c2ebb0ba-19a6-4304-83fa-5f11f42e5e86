---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: fe-ppe-status-bff
  name: fe-ppe-status-bff
  namespace: fe-ppe
spec:
  replicas: "${REPLICAS}"
  selector:
    matchLabels:
      app: fe-ppe-status-bff
  template:
    metadata:
      labels:
        app: fe-ppe-status-bff
    spec:
      securityContext:
        runAsUser: 1000
        runAsGroup: 1000
        fsGroup: 1000
      containers:
        - name: fe-ppe-status-bff
          image: ${image_repo}/fe-ppe-status-bff:${image_tag}
          ports:
            - containerPort: 3000
          env:
            - name: LOG_LEVEL
              value: "${LOG_LEVEL}"
            - name: SERVER_PORT
              value: "${SERVER_PORT}"
            - name: SERVER_READ_TIMEOUT
              value: "${SERVER_READ_TIMEOUT}"
            - name: HTTP_CLIENT_TIMEOUT
              value: "${HTTP_CLIENT_TIMEOUT}"
            - name: HTTP_CLIENT_RETRY_MAX
              value: "${HTTP_CLIENT_RETRY_MAX}"
            - name: LOGS_API_URL
              value: "${LOGS_API_URL}"
            - name: LOOKUP_API_URL
              value: "${LOOKUP_API_URL}"
            - name: RSU_API_URL
              value: "${RSU_API_URL}"
            - name: PPE_DEPLOYMENTS_API_URL
              value: "${PPE_DEPLOYMENTS_API_URL}"
            - name: IA_SITE_API_URL
              value: "${IA_SITE_API_URL}"
            - name: UCC_PROPS_API_URL
              value: "${UCC_PROPS_API_URL}"
            - name: KC_HOST
              value: "${KC_HOST}"
            - name: KC_REALM
              value: "${KC_REALM}"
