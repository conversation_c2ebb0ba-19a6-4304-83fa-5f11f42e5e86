resource "kubernetes_manifest" "fe_ppe_deployments_ui_deployment" {
  manifest = yamldecode(templatefile("../../common/ppe/fe-ppe-deployments-ui/base-deployment.yml", {
    image_repo = local.IMAGE_REPO
    image_tag  = local.fe-ppe-deployments-ui.version
    REPLICAS   = 1
    name       = "fe-ppe-deployments-ui"
  }))

  field_manager {
    force_conflicts = true
  }
}

resource "kubernetes_service" "fe_ppe_deployments_ui" {
  metadata {
    name      = "fe-ppe-deployments-ui"
    namespace = "fe-ppe"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "fe-ppe-deployments-ui"
    }
    port {
      port        = local.UI_SERVER_PORT
      target_port = local.UI_SERVER_PORT
    }
  }
}

# Tell prometheus to scrape these metrics
resource "kubernetes_manifest" "fe_ppe_deployments_ui_podmonitor" {
  manifest = yamldecode(templatefile("../../common/prometheus/podmonitor.yml.tftpl", {
    NAME        = "fe-ppe-deployments-ui"
    NAMESPACE   = local.NAMESPACE
    TARGET_PORT = local.PODMONITOR_PORT
  }))
}