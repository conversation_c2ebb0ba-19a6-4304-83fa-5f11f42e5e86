resource "kubernetes_manifest" "fe_ev_smsrcpt_io_deployment" {
  manifest = yamldecode(templatefile("../../common/ev/fe-ev-smsrcpt-io/base-deployment.yml", {
    name      = "fe-ev-smsrcpt-io"
    NAMESPACE = local.namespace
    REPLICAS  = 1

    image_repo = local.image_repo
    image_tag  = local.fe-ev-smsrcpt-io.version

    LOG_LEVEL                   = "info"
    SERVER_READ_TIMEOUT         = local.server_timeout
    METRICS_SERVER_PORT         = local.metrics_server_port

    CPU_REQUEST = "10m"
    MEM_REQUEST = "100Mi"

    WORKER_COUNT = "10"

    KC_HOST = "http://ia-keycloak-spi.kc-op.svc.cluster.local:8080"
    KC_REALM = "invenco-hub"
    AUTH_CLIENT_ID = "invenco-client"
    AUTH_SERVICE_USERNAME = "<EMAIL>"

    RTD_AUTH_ENABLED            = local.rtd_auth_enabled
    RTD_URL                     = local.rtd_url
    RTD_STREAM                  = "dp-ucc-telem-in"
    RTD_CLIENT_NAME             = "fe-ev-smsrcpt-io"
    RTD_SUBJECT_FILTERS          = "dp-ucc-telem-in.eds-finadv.rcptFwdReq.>"
    RTD_RETRY_ON_FAILED_CONNECT = true
    RTD_MAX_RECONNECTS          = -1
    RTD_BUFFER                  = 100
    
    IA_NOTIF_API_URL            = "http://ia-notif-api.ia.svc.cluster.local:8080"
    HTTP_CLIENT_TIMEOUT         = "30s"
    HTTP_CLIENT_RETRY_MAX       = "3"

    TRANSLATIONS_CONFIG_PATH    = "/app/config/translations.json"
    DEFAULT_TEXT_MESSAGE_TEMPLATE = "{{.Message}} https://konect-receipts.hub-dev.invenco.com/v1/{{.AuthRef}}/{{.ReceiptID}}"
    DEFAULT_LANGUAGE_CODE       = "en_US"
    DEFAULT_MESSAGE             = "Your EV charging session is complete. View your receipt here:"
    RELOAD_AFTER                = "60s"
    NOTIFY_UPDATE_EVERY         = "500ms"
  }))

  computed_fields = ["spec.template.metadata.annotations"]
  
  field_manager {
    force_conflicts = true
  }
}

resource "kubernetes_config_map_v1" "fe_ev_smsrcpt_io_config_map" {
  metadata {
    name      = "fe-ev-smsrcpt-io-config"
    namespace = local.namespace
  }

  data = {
    "translations.json" = jsonencode({
      "text_message_template" = "{{.Message}} https://konect-receipts.hub-dev.invenco.com/v1/{{.AuthRef}}/{{.ReceiptID}}"
      "default_translation" = {
        "language_code" = "en_US"
        "message"       = "Your EV charging session is complete. View your receipt here:"
      }
      "regex_patterns" = {
        "language_code_pattern"              = "^[a-z]{2}[-_][A-Z]{2}$"
        "international_phone_prefix_pattern" = "^\\+\\d{1,3}$"
      }
      "phone_prefix_configs" = [
        {
          "prefix"                = "+1"
          "default_language_code" = "en_US"
          "supported_language_codes" = [
            "en_US",
            "en_CA",
            "fr_CA"
          ],
          "translations" = {
            "en_US" = "Your EV charging session is complete. View your receipt here:"
          }
        }
      ]
    })
  }
}

resource "kubernetes_service" "fe_ev_smsrcpt_io_service" {
  metadata {
    name      = "fe-ev-smsrcpt-io"
    namespace = local.namespace
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "fe-ev-smsrcpt-io"
    }
    port {
      port        = local.server_port
      target_port = local.server_port
    }
    type = "NodePort"
  }
}

# Tell prometheus to scrape these metrics
resource "kubernetes_manifest" "fe_ev_smsrcpt_io_podmonitor" {
  manifest = yamldecode(templatefile("../../common/prometheus/podmonitor.yml.tftpl", {
    NAME        = "fe-ev-smsrcpt-io"
    NAMESPACE   = "fe-ev"
    TARGET_PORT = local.server_port
  }))
}
