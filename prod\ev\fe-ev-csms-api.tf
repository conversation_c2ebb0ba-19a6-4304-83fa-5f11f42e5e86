# US SIT
resource "kubernetes_manifest" "fe_ev_emsp_us_sit_deployment" {
  manifest = yamldecode(templatefile("../../common/ev/fe-ev-csms-api/base-deployment.yml", {
    name      = "fe-ev-emsp-api-us-sit"
    NAMESPACE = local.namespace
    REPLICAS  = 0

    image_repo = local.image_repo
    image_tag  = local.fe-ev-csms-api.version

    LOG_LEVEL       = "info"
    SERVER_PORT     = local.server_port
    SERVER_TIME_OUT = local.server_timeout

    MONGO_AUTH_SOURCE           = local.mongo_auth_source
    MONGO_DATABASE              = local.mongo_database_secure
    MONGO_HOST                  = local.mongo_hostname_secure
    MONGO_PORT                  = local.mongo_port
    MONGO_OPTIONS               = local.mongo_options_secure
    MONGO_SCHEMA                = local.mongo_schema
    MONGO_CREDENTIALS           = local.mongo_credentials_csms_api
    MONGO_CONNECTION_COLLECTION = local.mongo_connection_collection
    MONGO_DATABASE_COMMON       = local.mongo_database_common
    MONGO_LOCATION_COLLECTION   = local.mongo_location_collection
    MONGO_HOST_COMMON           = local.mongo_hostname_common
    MONGO_OPTIONS_COMMON        = local.mongo_options_secure

    BOOTSTRAP           = false
    CPO_API_URL         = "https://opna-test.myeverse.com/externalIncoming/ocpi/cpo"
    OCPI_BASE_URL       = "https://${local.fe-ev-csms-api.host}"
    PARTY_CODE          = "SIT"
    COUNTRY_CODE        = "US"
    CREDENTIALS_TOKEN_A = "7d1f59ec-e8bf-41b6-900c-464df6502690"
    AVAILABLE_VERSIONS  = join(",", local.ocpi_versions)
    AVAILABLE_MODULES   = join(",", local.ocpi_modules)

    LOCATION_URL        = local.location_url
    SESSION_URL         = local.session_url
    CDR_URL             = local.cdr_url
    TARIFF_URL          = local.tariff_url
    
    RTD_URL            = local.rtd_url
    RTD_STREAM         = local.rtd_stream
    RTD_CLIENT_NAME    = local.rtd_client_name
    RTD_SUBJECT_FILTER = local.rtd_location_subject_filter
    RTD_AUTH_ENABLED   = local.rtd_auth_enabled

    CPU_REQUEST = "10m"
    MEM_REQUEST = "100Mi"
    RESTY_DEBUG_ENABLED = false

  }))
  field_manager {
    force_conflicts = true
  }
}

resource "kubernetes_service" "fe_ev_emsp_us_sit_service" {
  metadata {
    name      = "fe-ev-ocpi-us-sit"
    namespace = local.namespace
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app   = "fe-ev-emsp-api-us-sit"
      cc    = "US"
      party = "SIT"
    }
    port {
      port        = local.server_port
      target_port = local.server_port
    }
    type = "NodePort"
  }
}


#ingress/alb
resource "kubernetes_manifest" "fe_ev_csms_api_ingress_v1" {
  manifest = yamldecode(templatefile("../../common/ev/fe-ev-csms-api/alb-ingress-v1.yml", {
    host        = local.fe-ev-csms-api.host
    NAMESPACE   = local.namespace
    SERVER_PORT = local.server_port
  }))

  field_manager {
    force_conflicts = true
  }
}

#Vault Dynamic Secret for MongoDB
resource "kubernetes_manifest" "fe_ev_csms_api_vds" {
  manifest = yamldecode(templatefile("../../common/ev/fe-ev-csms-api/vds.yml", {
    MONGO_ROLE = "fe-ev-csms-api"
    name       = "fe-ev-csms-api"
    env        = "prod"
  }))

  field_manager {
    force_conflicts = true
  }
}

# NATS stream
resource "kubernetes_manifest" "nats_stream" {
  manifest = yamldecode(templatefile("../../common/ev/fe-ev-csms-api/nats-stream.yml", {
    name       = "fe-ev-csms-api"
    REPLICAS   = 3
    NAMESPACE  = local.namespace
  }))

  field_manager {
    force_conflicts = true
  }
}

# Tell prometheus to scrape these metrics
resource "kubernetes_manifest" "fe_ev_csms_api_us_dom_podmonitor" {
  manifest = yamldecode(templatefile("../../common/prometheus/podmonitor.yml.tftpl", {
    NAME        = "fe-ev-csms-api-us-dom"
    NAMESPACE   = "fe-ev"
    TARGET_PORT = local.server_port
  }))
}

# Tell prometheus to scrape these metrics
resource "kubernetes_manifest" "fe_ev_csms_api_us_elv_podmonitor" {
  manifest = yamldecode(templatefile("../../common/prometheus/podmonitor.yml.tftpl", {
    NAME        = "fe-ev-csms-api-us-elv"
    NAMESPACE   = "fe-ev"
    TARGET_PORT = local.server_port
  }))
}
