resource "kubernetes_manifest" "fe_wsm_bff_deployment" {
  manifest = yamldecode(templatefile("../../common/wsm/fe-wsm-bff/base-deployment.yml", {
    image_repo = local.image_repo
    image_tag  = local.fe-wsm-bff.version
    REPLICAS   = 1

    CPU_REQUEST = "10m"
    MEM_REQUEST = "100Mi"

    LOG_LEVEL = "debug"
    SERVER_PORT = 8080

    IA_APP_REG_URI = local.appreg_api_url
    SERVER_READ_TIMEOUT = "5s"

    KEYCLOAK_HOST = local.keycloak_host
    KEYCLOAK_REALM = local.keycloak_realm

    HTTP_CLIENT_TIMEOUT = "30s"
    HTTP_CLIENT_RETRY_MAX = 3

    // Temporary values for RTC
    COMPANY_MOCK_TYPE = "dev"
    SITE_MOCK_TYPE = "dev"
    FPM_STATUS_MOCK_TYPE = "dev"

    TOPO_API_URL = local.topo_api_url
    INVENTORY_API_URL = "http://dp-wsm-inventory-api.dp.svc.cluster.local:8080"
    SALES_API_URL = "http://dp-wsm-sale-api.dp.svc.cluster.local:8080"
    MHR_RECON_API_URL = "http://dp-wsm-mhr-recon-api.dp.svc.cluster.local:8080"
    DAILY_SALE_API_URL = "http://dp-wsm-daily-sale-api.dp.svc.cluster.local:8080"
    FPM_STATUS_API_URL = "http://dp-wsm-fpm-status-api.dp.svc.cluster.local:8080"
    COMPANY_API_URL = "http://ia-company-api.ia.svc.cluster.local:8080"
    SITE_API_URL = "http://ia-site-api.ia.svc.cluster.local:8080"
    TANK_STATUS_API_URL = "http://dp-wsm-tank-status-api.dp.svc.cluster.local:8080"
    METER_VAR_API_URL = "http://dp-wsm-meter-var-api.dp.svc.cluster.local:8080"
    CONTRACT_API_URL = "http://ia-contract-api-v2.ia.svc.cluster.local:8080"
    DISPENSER_STATUS_API_URL = "http://dp-wsm-dispenser-status-api.dp.svc.cluster.local:8080"
    METER_FLOW_API_URL = "http://dp-wsm-meter-flow-api.dp.svc.cluster.local:8080"
  }))

  field_manager {
    force_conflicts = true
  }
}

resource "kubernetes_service" "fe_wsm_bff" {
  metadata {
    name      = "fe-wsm-bff"
    namespace = "fe-wsm"
    annotations = {
      "io.cilium/global-service" : "true"
      "io.cilium/shared-service" : "true"
    }
  }
  spec {
    selector = {
      app = "fe-wsm-bff"
    }
    port {
      port        = 8080
      target_port = 8080
    }

    type = "NodePort"
  }
}
